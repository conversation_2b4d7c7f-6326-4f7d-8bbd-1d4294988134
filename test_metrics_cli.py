#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
训练指标实时显示功能命令行测试
"""

import sys
import os
import time
import random
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

def test_metrics_widget():
    """测试指标显示组件"""
    print("=" * 60)
    print("测试训练指标显示组件")
    print("=" * 60)
    
    try:
        from PySide6.QtWidgets import QApplication
        from ui.widgets.training_progress_widget import MetricsDisplayWidget
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建指标显示组件
        widget = MetricsDisplayWidget()
        print("✅ 指标显示组件创建成功")
        
        # 测试指标更新功能
        print("\n📊 测试指标更新功能:")
        
        for epoch in range(1, 6):
            # 模拟训练指标
            metrics = {
                'epoch': epoch,
                'train/box_loss': 2.0 - (epoch * 0.3) + random.uniform(-0.1, 0.1),
                'val/box_loss': 1.8 - (epoch * 0.25) + random.uniform(-0.1, 0.1),
                'metrics/precision(B)': 0.3 + (epoch * 0.12) + random.uniform(-0.02, 0.02),
                'metrics/recall(B)': 0.25 + (epoch * 0.13) + random.uniform(-0.02, 0.02),
                'metrics/mAP50(B)': 0.2 + (epoch * 0.14) + random.uniform(-0.02, 0.02),
                'metrics/mAP50-95(B)': 0.15 + (epoch * 0.11) + random.uniform(-0.02, 0.02)
            }
            
            # 更新指标
            widget.update_metrics(metrics)
            
            print(f"  Epoch {epoch}:")
            print(f"    训练损失: {metrics['train/box_loss']:.4f}")
            print(f"    验证损失: {metrics['val/box_loss']:.4f}")
            print(f"    精确率: {metrics['metrics/precision(B)']:.4f}")
            print(f"    召回率: {metrics['metrics/recall(B)']:.4f}")
            print(f"    mAP50: {metrics['metrics/mAP50(B)']:.4f}")
            print(f"    mAP50-95: {metrics['metrics/mAP50-95(B)']:.4f}")
            print()
        
        print("✅ 指标更新测试成功")
        
        # 测试指标清除功能
        print("📝 测试指标清除功能:")
        widget.clear_metrics()
        print("✅ 指标清除测试成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 指标显示组件测试失败: {e}")
        return False

def test_progress_widget():
    """测试训练进度组件"""
    print("=" * 60)
    print("测试训练进度组件")
    print("=" * 60)
    
    try:
        from PySide6.QtWidgets import QApplication
        from ui.widgets.training_progress_widget import TrainingProgressWidget
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建训练进度组件
        widget = TrainingProgressWidget()
        print("✅ 训练进度组件创建成功")
        
        # 测试训练开始
        total_epochs = 10
        widget.start_training(total_epochs)
        print(f"✅ 训练开始测试成功 (总轮次: {total_epochs})")
        
        # 测试进度更新
        print("\n📊 测试进度更新:")
        for epoch in range(1, 6):
            # 更新轮次进度
            epoch_progress = random.randint(80, 100)
            widget.update_epoch_progress(epoch, epoch_progress)
            print(f"  Epoch {epoch}: {epoch_progress}%")
            
            # 更新训练指标
            metrics = {
                'epoch': epoch,
                'train/box_loss': 2.0 - (epoch * 0.3),
                'val/box_loss': 1.8 - (epoch * 0.25),
                'metrics/precision(B)': 0.3 + (epoch * 0.12),
                'metrics/recall(B)': 0.25 + (epoch * 0.13),
                'metrics/mAP50(B)': 0.2 + (epoch * 0.14),
                'metrics/mAP50-95(B)': 0.15 + (epoch * 0.11)
            }
            widget.update_training_metrics(metrics)
        
        print("✅ 进度更新测试成功")
        
        # 测试训练完成
        widget.finish_training(True)
        print("✅ 训练完成测试成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 训练进度组件测试失败: {e}")
        return False

def test_callback_integration():
    """测试回调集成功能"""
    print("=" * 60)
    print("测试回调集成功能")
    print("=" * 60)
    
    try:
        from core.trainer import YOLOTrainer
        
        # 创建训练器
        trainer = YOLOTrainer()
        print("✅ 训练器创建成功")
        
        # 测试回调设置
        callbacks = trainer.setup_training_callbacks()
        print("✅ 回调函数设置成功")
        
        # 验证回调函数
        expected_callbacks = ['on_train_epoch_end', 'on_val_end', 'on_train_start', 'on_train_end']
        all_callbacks_exist = True
        
        for callback_name in expected_callbacks:
            if callback_name in callbacks:
                print(f"  ✅ {callback_name} 回调存在")
            else:
                print(f"  ❌ {callback_name} 回调缺失")
                all_callbacks_exist = False
        
        if all_callbacks_exist:
            print("✅ 所有必需的回调函数都存在")
        else:
            print("❌ 部分回调函数缺失")
            return False
        
        # 测试回调函数调用（模拟）
        print("\n📞 测试回调函数调用:")
        
        # 模拟训练器对象
        class MockTrainer:
            def __init__(self):
                self.epoch = 0
                self.epochs = 5
                self.loss_items = [1.5, 0.8, 0.3]
                self.metrics = MockMetrics()
        
        class MockMetrics:
            def __init__(self):
                self.results_dict = {
                    'val/box_loss': 1.2,
                    'val/cls_loss': 0.6,
                    'metrics/precision(B)': 0.75,
                    'metrics/recall(B)': 0.68,
                    'metrics/mAP50(B)': 0.72,
                    'metrics/mAP50-95(B)': 0.45
                }
        
        # 设置回调函数
        trainer.metrics_callback = lambda metrics: print(f"    指标回调: {metrics}")
        trainer.log_callback = lambda msg: print(f"    日志回调: {msg}")
        trainer.progress_callback = lambda progress, msg: print(f"    进度回调: {progress}% - {msg}")
        trainer.is_training = True
        trainer.total_epochs = 5
        trainer.training_start_time = time.time()
        
        mock_trainer = MockTrainer()
        
        # 测试训练开始回调
        print("  测试 on_train_start:")
        callbacks['on_train_start'](mock_trainer)
        
        # 测试轮次结束回调
        print("  测试 on_train_epoch_end:")
        callbacks['on_train_epoch_end'](mock_trainer)
        
        # 测试验证结束回调
        print("  测试 on_val_end:")
        callbacks['on_val_end'](mock_trainer)
        
        # 测试训练结束回调
        print("  测试 on_train_end:")
        callbacks['on_train_end'](mock_trainer)
        
        print("✅ 回调函数调用测试成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 回调集成测试失败: {e}")
        return False

def main():
    """主函数"""
    print("训练指标实时显示功能测试")
    print("=" * 60)
    print("此测试验证训练指标实时显示功能的各个组件")
    print("不进行实际训练，只测试指标显示功能本身")
    print("=" * 60)
    
    # 测试结果
    results = []
    
    # 1. 测试指标显示组件
    print("\n1️⃣ 指标显示组件测试")
    results.append(test_metrics_widget())
    
    # 2. 测试训练进度组件
    print("\n2️⃣ 训练进度组件测试")
    results.append(test_progress_widget())
    
    # 3. 测试回调集成功能
    print("\n3️⃣ 回调集成功能测试")
    results.append(test_callback_integration())
    
    # 总结测试结果
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    passed_tests = sum(results)
    total_tests = len(results)
    
    test_names = [
        "指标显示组件",
        "训练进度组件", 
        "回调集成功能"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i+1}. {name}: {status}")
    
    print(f"\n总计: {passed_tests}/{total_tests} 项测试通过")
    
    if passed_tests == total_tests:
        print("\n🎉 所有测试通过！训练指标实时显示功能工作正常")
        print("\n✅ 验证的功能:")
        print("  • 实时指标数值显示和更新")
        print("  • 动态训练曲线绘制")
        print("  • 训练进度条和状态管理")
        print("  • 指标历史记录和清除")
        print("  • ultralytics回调机制集成")
        print("  • 线程安全的信号传递")
        
        print("\n📋 使用说明:")
        print("  1. 在主程序中切换到'模型训练'标签页")
        print("  2. 加载模型和数据集后开始训练")
        print("  3. 观察训练指标的实时更新和曲线变化")
        print("  4. 训练过程中可以查看详细的指标数值")
    else:
        print(f"\n❌ {total_tests - passed_tests} 项测试失败，请检查实现")

if __name__ == "__main__":
    main()
