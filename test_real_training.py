#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于实际训练进程的指标实时显示功能测试
"""

import sys
import os
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel, QTextEdit
from PySide6.QtCore import QTimer
from ui.widgets.training_progress_widget import TrainingProgressWidget
from core.trainer import YOLOTrainer

class RealTrainingTestWindow(QMainWindow):
    """实际训练测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("实际训练指标实时显示测试")
        self.setGeometry(100, 100, 1400, 900)
        
        # 创建中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 测试信息标签
        info_label = QLabel("实际训练测试 - 基于datasets数据集和YOLOv8n-seg.pt模型")
        info_label.setStyleSheet("font-size: 14px; font-weight: bold; color: blue; padding: 10px;")
        layout.addWidget(info_label)
        
        # 训练参数显示
        params_label = QLabel("训练参数: batch=4, epochs=25, device=cuda, workers=0, model=YOLOv8n-seg.pt")
        params_label.setStyleSheet("font-size: 12px; color: green; padding: 5px;")
        layout.addWidget(params_label)
        
        # 创建训练进度组件
        self.progress_widget = TrainingProgressWidget()
        layout.addWidget(self.progress_widget)
        
        # 创建控制按钮
        button_layout = QVBoxLayout()
        
        self.start_btn = QPushButton("开始实际训练测试")
        self.start_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-size: 14px; padding: 10px; }")
        self.stop_btn = QPushButton("停止训练")
        self.stop_btn.setStyleSheet("QPushButton { background-color: #f44336; color: white; font-size: 14px; padding: 10px; }")
        self.stop_btn.setEnabled(False)
        
        button_layout.addWidget(self.start_btn)
        button_layout.addWidget(self.stop_btn)
        layout.addLayout(button_layout)
        
        # 日志显示区域
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setPlaceholderText("训练日志将在这里显示...")
        layout.addWidget(QLabel("训练日志:"))
        layout.addWidget(self.log_text)
        
        # 连接信号
        self.start_btn.clicked.connect(self.start_real_training)
        self.stop_btn.clicked.connect(self.stop_training)
        
        # 初始化训练器
        self.trainer = YOLOTrainer()
        self.training_thread = None
        
    def log_message(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
        
    def start_real_training(self):
        """开始实际训练测试"""
        try:
            self.log_message("开始实际训练测试...")
            
            # 检查模型文件
            model_path = "models/YOLOv8n-seg.pt"
            if not os.path.exists(model_path):
                self.log_message(f"错误: 模型文件不存在 - {model_path}")
                return
            
            # 检查数据集配置
            data_config = "datasets/data.yaml"
            if not os.path.exists(data_config):
                self.log_message(f"错误: 数据集配置文件不存在 - {data_config}")
                return
                
            self.log_message(f"加载模型: {model_path}")
            
            # 加载模型
            success = self.trainer.load_custom_model(model_path)
            if not success:
                self.log_message("错误: 模型加载失败")
                return
                
            self.log_message("模型加载成功")
            
            # 设置训练参数
            training_config = {
                'epochs': 25,
                'batch': 4,
                'device': 'cuda',
                'workers': 0,
                'imgsz': 640,
                'lr0': 0.01,
                'lrf': 0.01,
                'momentum': 0.937,
                'weight_decay': 0.0005,
                'warmup_epochs': 3.0,
                'amp': True,
                'cache': False,
                'plots': True,
                'save': True,
                'val': True,
                'verbose': True
            }
            
            self.trainer.set_training_config(training_config)
            self.log_message("训练参数设置完成")
            
            # 启动训练进度组件
            self.progress_widget.start_training(25)
            
            # 更新UI状态
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            
            self.log_message("开始训练...")
            
            # 开始训练（在主线程中，这样可以看到实时更新）
            success = self.trainer.train(
                data_config,
                self.on_progress_update,
                self.on_log_update,
                self.on_metrics_update
            )
            
            # 训练完成
            self.progress_widget.finish_training(success)
            
            if success:
                self.log_message("训练成功完成！")
            else:
                self.log_message("训练失败")
                
        except Exception as e:
            self.log_message(f"训练过程出错: {str(e)}")
            self.progress_widget.finish_training(False)
        finally:
            # 恢复UI状态
            self.start_btn.setEnabled(True)
            self.stop_btn.setEnabled(False)
    
    def stop_training(self):
        """停止训练"""
        self.log_message("请求停止训练...")
        if self.trainer:
            self.trainer.stop_training()
        
    def on_progress_update(self, progress, message):
        """进度更新回调"""
        self.log_message(f"进度: {progress}% - {message}")
        
    def on_log_update(self, message):
        """日志更新回调"""
        self.log_message(f"训练: {message}")
        
    def on_metrics_update(self, metrics):
        """指标更新回调"""
        # 更新进度组件
        self.progress_widget.update_training_metrics(metrics)
        
        # 记录关键指标
        if 'epoch' in metrics:
            epoch = metrics['epoch']
            log_parts = [f"Epoch {epoch}"]
            
            if 'train/box_loss' in metrics:
                log_parts.append(f"train_loss={metrics['train/box_loss']:.4f}")
            if 'val/box_loss' in metrics:
                log_parts.append(f"val_loss={metrics['val/box_loss']:.4f}")
            if 'metrics/mAP50(B)' in metrics:
                log_parts.append(f"mAP50={metrics['metrics/mAP50(B)']:.4f}")
                
            self.log_message(" | ".join(log_parts))
            
            # 更新轮次进度
            self.progress_widget.update_epoch_progress(epoch, 100)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = RealTrainingTestWindow()
    window.show()
    
    print("实际训练指标实时显示测试程序已启动")
    print("测试配置:")
    print("- 数据集: datasets/")
    print("- 模型: models/YOLOv8n-seg.pt")
    print("- 参数: batch=4, epochs=25, device=cuda, workers=0")
    print("点击'开始实际训练测试'按钮开始测试")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
