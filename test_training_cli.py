#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
命令行版本的实际训练测试
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

from core.trainer import YOLOTrainer

def test_training_setup():
    """测试训练设置"""
    print("=" * 60)
    print("实际训练指标实时显示功能测试")
    print("=" * 60)
    
    # 检查文件
    model_path = "models/YOLOv8n-seg.pt"
    data_config = "datasets/data.yaml"
    
    print(f"检查模型文件: {model_path}")
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return False
    print(f"✅ 模型文件存在")
    
    print(f"检查数据集配置: {data_config}")
    if not os.path.exists(data_config):
        print(f"❌ 数据集配置文件不存在: {data_config}")
        return False
    print(f"✅ 数据集配置文件存在")
    
    # 检查数据集目录
    dataset_dirs = ["datasets/train/images", "datasets/val/images", "datasets/train/labels", "datasets/val/labels"]
    for dir_path in dataset_dirs:
        if not os.path.exists(dir_path):
            print(f"❌ 数据集目录不存在: {dir_path}")
            return False
        file_count = len(os.listdir(dir_path))
        print(f"✅ {dir_path}: {file_count} 个文件")
    
    return True

def test_trainer_initialization():
    """测试训练器初始化"""
    print("\n" + "=" * 60)
    print("测试训练器初始化")
    print("=" * 60)
    
    try:
        # 创建训练器
        trainer = YOLOTrainer()
        print("✅ 训练器创建成功")
        
        # 加载模型
        model_path = "models/YOLOv8n-seg.pt"
        success = trainer.load_custom_model(model_path)
        if not success:
            print("❌ 模型加载失败")
            return None
        print("✅ 模型加载成功")
        
        # 获取模型信息
        model_info = trainer.get_model_info()
        print(f"✅ 模型信息: {model_info}")
        
        # 设置训练配置
        training_config = {
            'epochs': 25,
            'batch': 4,
            'device': 'cuda',
            'workers': 0,
            'imgsz': 640,
            'lr0': 0.01,
            'lrf': 0.01,
            'momentum': 0.937,
            'weight_decay': 0.0005,
            'warmup_epochs': 3.0,
            'amp': True,
            'cache': False,
            'plots': True,
            'save': True,
            'val': True,
            'verbose': True
        }
        
        trainer.set_training_config(training_config)
        print("✅ 训练配置设置成功")
        
        return trainer
        
    except Exception as e:
        print(f"❌ 训练器初始化失败: {e}")
        return None

def test_callbacks():
    """测试回调函数"""
    print("\n" + "=" * 60)
    print("测试回调函数")
    print("=" * 60)
    
    try:
        trainer = YOLOTrainer()
        
        # 测试回调设置
        callbacks = trainer.setup_training_callbacks()
        print("✅ 回调函数设置成功")
        
        expected_callbacks = ['on_train_epoch_end', 'on_val_end', 'on_train_start', 'on_train_end']
        for callback_name in expected_callbacks:
            if callback_name in callbacks:
                print(f"  ✅ {callback_name} 回调存在")
            else:
                print(f"  ❌ {callback_name} 回调缺失")
        
        return True
        
    except Exception as e:
        print(f"❌ 回调函数测试失败: {e}")
        return False

def test_short_training():
    """测试短时间训练（1个epoch）"""
    print("\n" + "=" * 60)
    print("测试短时间训练 (1 epoch)")
    print("=" * 60)
    
    try:
        # 创建训练器
        trainer = YOLOTrainer()
        
        # 加载模型
        model_path = "models/YOLOv8n-seg.pt"
        success = trainer.load_custom_model(model_path)
        if not success:
            print("❌ 模型加载失败")
            return False
        
        # 设置短训练配置
        training_config = {
            'epochs': 1,  # 只训练1个epoch用于测试
            'batch': 4,
            'device': 'cuda',
            'workers': 0,
            'imgsz': 640,
            'lr0': 0.01,
            'lrf': 0.01,
            'momentum': 0.937,
            'weight_decay': 0.0005,
            'warmup_epochs': 0,  # 不使用预热
            'amp': True,
            'cache': False,
            'plots': True,
            'save': True,
            'val': True,
            'verbose': True
        }
        
        trainer.set_training_config(training_config)
        
        # 定义回调函数
        def progress_callback(progress, message):
            print(f"📊 进度: {progress}% - {message}")
        
        def log_callback(message):
            print(f"📝 日志: {message}")
        
        def metrics_callback(metrics):
            print(f"📈 指标: {metrics}")
        
        print("开始短时间训练测试...")
        
        # 开始训练
        data_config = "datasets/data.yaml"
        success = trainer.train(
            data_config,
            progress_callback,
            log_callback,
            metrics_callback
        )
        
        if success:
            print("✅ 短时间训练测试成功完成")
            return True
        else:
            print("❌ 短时间训练测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 短时间训练测试出错: {e}")
        return False

def main():
    """主函数"""
    print("开始实际训练指标实时显示功能测试...")
    
    # 1. 测试训练设置
    if not test_training_setup():
        print("\n❌ 训练设置测试失败，退出")
        return
    
    # 2. 测试训练器初始化
    trainer = test_trainer_initialization()
    if trainer is None:
        print("\n❌ 训练器初始化测试失败，退出")
        return
    
    # 3. 测试回调函数
    if not test_callbacks():
        print("\n❌ 回调函数测试失败，退出")
        return
    
    # 4. 询问是否进行实际训练测试
    print("\n" + "=" * 60)
    print("准备进行实际训练测试")
    print("=" * 60)
    print("注意: 这将进行1个epoch的实际训练来测试指标实时显示功能")
    print("预计需要几分钟时间，取决于您的硬件配置")
    
    response = input("\n是否继续进行实际训练测试? (y/n): ").lower().strip()
    
    if response == 'y' or response == 'yes':
        print("\n开始实际训练测试...")
        success = test_short_training()
        
        if success:
            print("\n🎉 所有测试通过！训练指标实时显示功能工作正常")
            print("\n功能验证:")
            print("✅ 模型加载和配置")
            print("✅ 数据集验证")
            print("✅ 训练器初始化")
            print("✅ 回调函数设置")
            print("✅ 实际训练过程")
            print("✅ 指标实时提取和显示")
        else:
            print("\n❌ 实际训练测试失败")
    else:
        print("\n跳过实际训练测试")
        print("✅ 基础功能测试已通过，可以手动进行完整训练测试")

if __name__ == "__main__":
    main()
