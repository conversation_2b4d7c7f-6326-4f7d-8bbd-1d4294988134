#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
训练指标实时显示功能测试
专门测试指标显示组件的功能，不进行实际训练
"""

import sys
import os
import time
import random
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel, QHBoxLayout
from PySide6.QtCore import QTimer
from ui.widgets.training_progress_widget import TrainingProgressWidget, MetricsDisplayWidget

class MetricsDisplayTestWindow(QMainWindow):
    """训练指标实时显示功能测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("训练指标实时显示功能测试")
        self.setGeometry(100, 100, 1400, 900)
        
        # 创建中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("训练指标实时显示功能测试")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #2E86AB; padding: 10px;")
        layout.addWidget(title_label)
        
        # 说明
        desc_label = QLabel("此测试模拟真实训练过程中的指标更新，验证指标显示组件的实时更新功能")
        desc_label.setStyleSheet("font-size: 12px; color: #666; padding: 5px;")
        layout.addWidget(desc_label)
        
        # 创建训练进度组件
        self.progress_widget = TrainingProgressWidget()
        layout.addWidget(self.progress_widget)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        self.start_btn = QPushButton("开始指标显示测试")
        self.start_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-size: 14px; padding: 10px; }")
        
        self.pause_btn = QPushButton("暂停")
        self.pause_btn.setStyleSheet("QPushButton { background-color: #FF9800; color: white; font-size: 14px; padding: 10px; }")
        self.pause_btn.setEnabled(False)
        
        self.stop_btn = QPushButton("停止测试")
        self.stop_btn.setStyleSheet("QPushButton { background-color: #f44336; color: white; font-size: 14px; padding: 10px; }")
        self.stop_btn.setEnabled(False)
        
        self.clear_btn = QPushButton("清除指标")
        self.clear_btn.setStyleSheet("QPushButton { background-color: #607D8B; color: white; font-size: 14px; padding: 10px; }")
        
        button_layout.addWidget(self.start_btn)
        button_layout.addWidget(self.pause_btn)
        button_layout.addWidget(self.stop_btn)
        button_layout.addWidget(self.clear_btn)
        layout.addLayout(button_layout)
        
        # 测试状态显示
        self.status_label = QLabel("就绪 - 点击开始测试")
        self.status_label.setStyleSheet("font-size: 12px; color: #333; padding: 5px; background-color: #f0f0f0;")
        layout.addWidget(self.status_label)
        
        # 连接信号
        self.start_btn.clicked.connect(self.start_test)
        self.pause_btn.clicked.connect(self.pause_test)
        self.stop_btn.clicked.connect(self.stop_test)
        self.clear_btn.clicked.connect(self.clear_metrics)
        
        # 模拟定时器
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_metrics)
        
        # 测试参数
        self.current_epoch = 0
        self.total_epochs = 25
        self.is_running = False
        self.is_paused = False
        
    def start_test(self):
        """开始指标显示测试"""
        if self.is_running:
            return
            
        self.is_running = True
        self.is_paused = False
        self.current_epoch = 0
        
        # 启动训练进度组件
        self.progress_widget.start_training(self.total_epochs)
        
        # 启动模拟定时器（每1.5秒一个epoch）
        self.timer.start(1500)
        
        # 更新UI状态
        self.start_btn.setEnabled(False)
        self.pause_btn.setEnabled(True)
        self.stop_btn.setEnabled(True)
        
        self.status_label.setText("测试运行中 - 模拟训练指标实时更新")
        self.status_label.setStyleSheet("font-size: 12px; color: white; padding: 5px; background-color: #4CAF50;")
        
    def pause_test(self):
        """暂停/恢复测试"""
        if not self.is_running:
            return
            
        if self.is_paused:
            # 恢复
            self.timer.start(1500)
            self.is_paused = False
            self.pause_btn.setText("暂停")
            self.status_label.setText("测试运行中 - 模拟训练指标实时更新")
            self.status_label.setStyleSheet("font-size: 12px; color: white; padding: 5px; background-color: #4CAF50;")
        else:
            # 暂停
            self.timer.stop()
            self.is_paused = True
            self.pause_btn.setText("恢复")
            self.status_label.setText("测试已暂停")
            self.status_label.setStyleSheet("font-size: 12px; color: white; padding: 5px; background-color: #FF9800;")
        
    def stop_test(self):
        """停止测试"""
        if not self.is_running:
            return
            
        self.is_running = False
        self.is_paused = False
        self.timer.stop()
        
        # 完成训练进度组件
        self.progress_widget.finish_training(True)
        
        # 更新UI状态
        self.start_btn.setEnabled(True)
        self.pause_btn.setEnabled(False)
        self.pause_btn.setText("暂停")
        self.stop_btn.setEnabled(False)
        
        self.status_label.setText("测试已停止")
        self.status_label.setStyleSheet("font-size: 12px; color: white; padding: 5px; background-color: #f44336;")
        
    def clear_metrics(self):
        """清除指标"""
        self.progress_widget.metrics_widget.clear_metrics()
        self.status_label.setText("指标已清除")
        self.status_label.setStyleSheet("font-size: 12px; color: white; padding: 5px; background-color: #607D8B;")
        
    def update_metrics(self):
        """更新模拟指标"""
        if not self.is_running or self.is_paused:
            return
            
        self.current_epoch += 1
        
        # 模拟真实训练指标变化趋势
        progress_ratio = self.current_epoch / self.total_epochs
        
        # 训练损失：从高到低，带随机波动
        base_train_loss = max(0.1, 2.5 - (progress_ratio * 2.0))
        train_loss = base_train_loss + random.uniform(-0.15, 0.15)
        
        # 验证损失：从高到低，比训练损失稍高
        base_val_loss = max(0.12, 2.2 - (progress_ratio * 1.8))
        val_loss = base_val_loss + random.uniform(-0.12, 0.12)
        
        # 精确率：从低到高
        base_precision = min(0.95, 0.3 + (progress_ratio * 0.6))
        precision = base_precision + random.uniform(-0.05, 0.05)
        
        # 召回率：从低到高
        base_recall = min(0.92, 0.25 + (progress_ratio * 0.65))
        recall = base_recall + random.uniform(-0.05, 0.05)
        
        # mAP50：从低到高
        base_map50 = min(0.88, 0.2 + (progress_ratio * 0.65))
        map50 = base_map50 + random.uniform(-0.04, 0.04)
        
        # mAP50-95：从低到高，比mAP50低
        base_map50_95 = min(0.75, 0.15 + (progress_ratio * 0.55))
        map50_95 = base_map50_95 + random.uniform(-0.04, 0.04)
        
        # 构建指标字典
        metrics = {
            'epoch': self.current_epoch,
            'train/box_loss': max(0.05, train_loss),
            'val/box_loss': max(0.05, val_loss),
            'metrics/precision(B)': max(0.0, min(1.0, precision)),
            'metrics/recall(B)': max(0.0, min(1.0, recall)),
            'metrics/mAP50(B)': max(0.0, min(1.0, map50)),
            'metrics/mAP50-95(B)': max(0.0, min(1.0, map50_95))
        }
        
        # 更新训练指标
        self.progress_widget.update_training_metrics(metrics)
        
        # 更新轮次进度
        epoch_progress = random.randint(85, 100)  # 模拟轮次内进度
        self.progress_widget.update_epoch_progress(self.current_epoch, epoch_progress)
        
        # 更新状态
        self.status_label.setText(
            f"Epoch {self.current_epoch}/{self.total_epochs} | "
            f"train_loss: {metrics['train/box_loss']:.3f} | "
            f"val_loss: {metrics['val/box_loss']:.3f} | "
            f"mAP50: {metrics['metrics/mAP50(B)']:.3f}"
        )
        
        # 检查是否完成
        if self.current_epoch >= self.total_epochs:
            self.stop_test()

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = MetricsDisplayTestWindow()
    window.show()
    
    print("训练指标实时显示功能测试程序已启动")
    print("=" * 50)
    print("测试功能:")
    print("✓ 实时指标数值显示")
    print("✓ 动态训练曲线更新")
    print("✓ 进度条和时间估算")
    print("✓ 指标历史记录管理")
    print("✓ 界面响应和控制")
    print("=" * 50)
    print("操作说明:")
    print("1. 点击'开始指标显示测试'开始模拟")
    print("2. 观察指标数值和曲线的实时更新")
    print("3. 可以暂停/恢复测试过程")
    print("4. 可以随时停止测试")
    print("5. 可以清除指标重新开始")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
