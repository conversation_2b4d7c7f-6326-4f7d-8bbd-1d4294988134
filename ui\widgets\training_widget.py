# YOLO模型训练界面组件
import os
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGroupBox,
                               QLabel, QPushButton, QLineEdit, QSpinBox, QDoubleSpinBox,
                               QComboBox, QCheckBox, QTextEdit, QScrollArea,
                               QFileDialog, QMessageBox, QTabWidget, QFormLayout)
from PySide6.QtCore import QThread, Signal, Qt

from core.trainer import YOLOTrainer
from .training_progress_widget import TrainingProgressWidget

class ModelDownloadThread(QThread):
    """模型下载线程"""
    
    progressUpdate = Signal(int, str)  # 进度和状态消息
    finished = Signal(bool, str)  # 完成状态和消息
    
    def __init__(self, trainer, model_name, save_dir):
        super().__init__()
        self.trainer = trainer
        self.model_name = model_name
        self.save_dir = save_dir
    
    def run(self):
        """运行下载"""
        try:
            success = self.trainer.download_pretrained_model(
                self.model_name, 
                self.save_dir,
                self.progress_callback
            )
            
            if success:
                # 加载下载的模型
                model_loaded = self.trainer.load_pretrained_model(self.model_name, self.save_dir)
                if model_loaded:
                    self.finished.emit(True, f"模型 {self.model_name} 下载并加载成功")
                else:
                    self.finished.emit(False, f"模型下载成功但加载失败")
            else:
                self.finished.emit(False, f"模型 {self.model_name} 下载失败")
                
        except Exception as e:
            self.finished.emit(False, f"下载过程出错: {str(e)}")
    
    def progress_callback(self, progress, message):
        """进度回调"""
        self.progressUpdate.emit(progress, message)

class TrainingThread(QThread):
    """训练线程"""

    progressUpdate = Signal(int, str)  # 进度和状态消息
    metricsUpdate = Signal(dict)  # 训练指标更新
    logUpdate = Signal(str)  # 日志更新
    finished = Signal(bool, str)  # 完成状态和消息

    def __init__(self, trainer, data_config):
        super().__init__()
        self.trainer = trainer
        self.data_config = data_config
        self._stop_requested = False
    
    def run(self):
        """运行训练"""
        try:
            # 训练调用，传递所有回调函数
            success = self.trainer.train(
                self.data_config,
                self.progress_callback,
                self.log_callback,
                self.metrics_callback
            )

            if self._stop_requested:
                self.finished.emit(False, "训练被用户停止")
            elif success:
                self.finished.emit(True, "训练完成")
            else:
                self.finished.emit(False, "训练失败")

        except KeyboardInterrupt:
            self.finished.emit(False, "训练被强制停止")
        except Exception as e:
            if self._stop_requested:
                self.finished.emit(False, "训练被用户停止")
            else:
                self.finished.emit(False, f"训练过程出错: {str(e)}")

    def stop_training(self):
        """停止训练"""
        self._stop_requested = True
        if self.trainer:
            self.trainer.is_training = False
            self.trainer.stop_training()

    def force_stop(self):
        """强制停止训练进程"""
        self._stop_requested = True
        if self.trainer:
            self.trainer.is_training = False
        # 强制终止线程
        self.terminate()
        self.wait(1000)  # 等待1秒

    def progress_callback(self, progress, message):
        """进度回调"""
        if not self._stop_requested:
            self.progressUpdate.emit(progress, message)

    def metrics_callback(self, metrics):
        """指标回调"""
        if not self._stop_requested:
            self.metricsUpdate.emit(metrics)

    def log_callback(self, message):
        """日志回调"""
        if not self._stop_requested:
            self.logUpdate.emit(message)

class TrainingWidget(QWidget):
    """训练界面组件 - 简化版"""
    
    def __init__(self):
        super().__init__()

        # 初始化训练器
        self.trainer = YOLOTrainer()
        self.download_thread = None
        self.training_thread = None

        # 当前数据集路径
        self.current_dataset = None
        self.current_data_config = None

        self.setup_ui()
        self.connect_signals()
        self.load_pretrained_models()

    def setup_ui(self):
        """设置UI界面"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # 创建滚动内容容器
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)

        # 创建配置面板
        config_panel = self.create_config_panel()
        scroll_layout.addWidget(config_panel)

        # 添加训练进度组件
        self.progress_widget = TrainingProgressWidget()
        scroll_layout.addWidget(self.progress_widget)

        # 设置滚动内容
        scroll_area.setWidget(scroll_content)
        main_layout.addWidget(scroll_area)
    
    def create_config_panel(self) -> QWidget:
        """创建配置面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 创建标签页
        tab_widget = QTabWidget()
        
        # 模型选择标签页
        model_tab = self.create_model_tab()
        tab_widget.addTab(model_tab, "模型选择")
        
        # 数据集标签页
        dataset_tab = self.create_dataset_tab()
        tab_widget.addTab(dataset_tab, "数据集")
        
        # 训练参数标签页
        params_tab = self.create_params_tab()
        tab_widget.addTab(params_tab, "训练参数")
        
        layout.addWidget(tab_widget)
        
        # 训练控制按钮
        control_group = QGroupBox("训练控制")
        control_layout = QVBoxLayout(control_group)
        
        self.start_training_btn = QPushButton("开始训练")
        self.stop_training_btn = QPushButton("停止训练")
        self.validate_model_btn = QPushButton("验证模型")
        
        self.start_training_btn.setEnabled(False)
        self.stop_training_btn.setEnabled(False)
        self.validate_model_btn.setEnabled(False)
        
        control_layout.addWidget(self.start_training_btn)
        control_layout.addWidget(self.stop_training_btn)
        control_layout.addWidget(self.validate_model_btn)
        
        layout.addWidget(control_group)
        
        return panel
    
    def create_model_tab(self) -> QWidget:
        """创建模型选择标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 预训练模型组
        pretrained_group = QGroupBox("预训练模型")
        pretrained_layout = QVBoxLayout(pretrained_group)
        
        self.model_combo = QComboBox()
        self.download_model_btn = QPushButton("下载模型")
        self.model_status_label = QLabel("未选择模型")
        self.model_status_label.setStyleSheet("color: gray;")

        pretrained_layout.addWidget(QLabel("选择预训练模型:"))
        pretrained_layout.addWidget(self.model_combo)
        pretrained_layout.addWidget(self.download_model_btn)
        pretrained_layout.addWidget(self.model_status_label)
        
        layout.addWidget(pretrained_group)
        
        # 自定义模型组
        custom_group = QGroupBox("自定义模型")
        custom_layout = QVBoxLayout(custom_group)
        
        self.custom_model_path = QLineEdit()
        self.custom_model_path.setPlaceholderText("选择自定义模型文件...")
        self.browse_model_btn = QPushButton("浏览")
        self.load_custom_btn = QPushButton("加载自定义模型")
        
        custom_model_layout = QHBoxLayout()
        custom_model_layout.addWidget(self.custom_model_path)
        custom_model_layout.addWidget(self.browse_model_btn)
        
        custom_layout.addWidget(QLabel("自定义模型路径:"))
        custom_layout.addLayout(custom_model_layout)
        custom_layout.addWidget(self.load_custom_btn)
        
        layout.addWidget(custom_group)
        
        # 模型信息
        info_group = QGroupBox("模型信息")
        info_layout = QVBoxLayout(info_group)
        
        self.model_info_text = QTextEdit()
        self.model_info_text.setMaximumHeight(100)
        self.model_info_text.setReadOnly(True)
        self.model_info_text.setPlaceholderText("模型信息将在这里显示...")
        
        info_layout.addWidget(self.model_info_text)
        layout.addWidget(info_group)
        
        return widget
    
    def create_dataset_tab(self) -> QWidget:
        """创建数据集标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 数据集路径组
        dataset_group = QGroupBox("数据集路径")
        dataset_layout = QVBoxLayout(dataset_group)
        
        self.dataset_path = QLineEdit()
        self.dataset_path.setPlaceholderText("选择数据集目录...")
        self.browse_dataset_btn = QPushButton("浏览")
        self.validate_dataset_btn = QPushButton("验证数据集")
        
        dataset_path_layout = QHBoxLayout()
        dataset_path_layout.addWidget(self.dataset_path)
        dataset_path_layout.addWidget(self.browse_dataset_btn)
        
        dataset_layout.addWidget(QLabel("数据集目录:"))
        dataset_layout.addLayout(dataset_path_layout)
        dataset_layout.addWidget(self.validate_dataset_btn)
        
        layout.addWidget(dataset_group)
        
        # 数据集信息
        info_group = QGroupBox("数据集信息")
        info_layout = QVBoxLayout(info_group)
        
        self.dataset_info_text = QTextEdit()
        self.dataset_info_text.setMaximumHeight(150)
        self.dataset_info_text.setReadOnly(True)
        self.dataset_info_text.setPlaceholderText("数据集信息将在这里显示...")
        
        info_layout.addWidget(self.dataset_info_text)
        layout.addWidget(info_group)

        return widget

    def create_params_tab(self) -> QWidget:
        """创建训练参数标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 基础参数
        basic_group = QGroupBox("基础参数")
        basic_layout = QFormLayout(basic_group)

        self.epochs_spin = QSpinBox()
        self.epochs_spin.setRange(1, 1000)
        self.epochs_spin.setValue(100)
        basic_layout.addRow("训练轮数 (epochs):", self.epochs_spin)

        self.batch_spin = QSpinBox()
        self.batch_spin.setRange(1, 128)
        self.batch_spin.setValue(16)
        basic_layout.addRow("批次大小 (batch):", self.batch_spin)

        self.imgsz_spin = QSpinBox()
        self.imgsz_spin.setRange(320, 1280)
        self.imgsz_spin.setValue(640)
        self.imgsz_spin.setSingleStep(32)
        basic_layout.addRow("图像尺寸 (imgsz):", self.imgsz_spin)

        self.device_combo = QComboBox()
        self.device_combo.addItems(['auto', 'cpu', 'cuda'])
        basic_layout.addRow("设备 (device):", self.device_combo)

        layout.addWidget(basic_group)

        # 学习率参数
        lr_group = QGroupBox("学习率参数")
        lr_layout = QFormLayout(lr_group)

        self.lr0_spin = QDoubleSpinBox()
        self.lr0_spin.setRange(0.0001, 1.0)
        self.lr0_spin.setValue(0.01)
        self.lr0_spin.setDecimals(4)
        self.lr0_spin.setSingleStep(0.001)
        lr_layout.addRow("初始学习率 (lr0):", self.lr0_spin)

        self.lrf_spin = QDoubleSpinBox()
        self.lrf_spin.setRange(0.0001, 1.0)
        self.lrf_spin.setValue(0.01)
        self.lrf_spin.setDecimals(4)
        self.lrf_spin.setSingleStep(0.001)
        lr_layout.addRow("最终学习率 (lrf):", self.lrf_spin)

        self.momentum_spin = QDoubleSpinBox()
        self.momentum_spin.setRange(0.0, 1.0)
        self.momentum_spin.setValue(0.937)
        self.momentum_spin.setDecimals(3)
        self.momentum_spin.setSingleStep(0.01)
        lr_layout.addRow("动量 (momentum):", self.momentum_spin)

        self.weight_decay_spin = QDoubleSpinBox()
        self.weight_decay_spin.setRange(0.0, 0.01)
        self.weight_decay_spin.setValue(0.0005)
        self.weight_decay_spin.setDecimals(4)
        self.weight_decay_spin.setSingleStep(0.0001)
        lr_layout.addRow("权重衰减 (weight_decay):", self.weight_decay_spin)

        layout.addWidget(lr_group)

        # 高级参数
        advanced_group = QGroupBox("高级参数")
        advanced_layout = QFormLayout(advanced_group)

        self.warmup_epochs_spin = QDoubleSpinBox()
        self.warmup_epochs_spin.setRange(0.0, 10.0)
        self.warmup_epochs_spin.setValue(3.0)
        self.warmup_epochs_spin.setDecimals(1)
        advanced_layout.addRow("预热轮数 (warmup_epochs):", self.warmup_epochs_spin)

        self.workers_spin = QSpinBox()
        self.workers_spin.setRange(0, 16)
        self.workers_spin.setValue(8)
        advanced_layout.addRow("工作进程数 (workers):", self.workers_spin)

        self.optimizer_combo = QComboBox()
        self.optimizer_combo.addItems(['auto', 'SGD', 'Adam', 'AdamW', 'RMSProp'])
        advanced_layout.addRow("优化器 (optimizer):", self.optimizer_combo)

        # 布尔参数
        self.amp_check = QCheckBox("混合精度训练 (amp)")
        self.amp_check.setChecked(True)
        advanced_layout.addRow(self.amp_check)

        self.cache_check = QCheckBox("缓存图像 (cache)")
        self.cache_check.setChecked(False)
        advanced_layout.addRow(self.cache_check)

        self.plots_check = QCheckBox("生成训练图表 (plots)")
        self.plots_check.setChecked(True)
        advanced_layout.addRow(self.plots_check)

        layout.addWidget(advanced_group)

        return widget

    def connect_signals(self):
        """连接信号"""
        # 模型相关
        self.download_model_btn.clicked.connect(self.download_model)
        self.browse_model_btn.clicked.connect(self.browse_custom_model)
        self.load_custom_btn.clicked.connect(self.load_custom_model)

        # 数据集相关
        self.browse_dataset_btn.clicked.connect(self.browse_dataset)
        self.validate_dataset_btn.clicked.connect(self.validate_dataset)

        # 训练控制
        self.start_training_btn.clicked.connect(self.start_training)
        self.stop_training_btn.clicked.connect(self.stop_training)
        self.validate_model_btn.clicked.connect(self.validate_model)

    def load_pretrained_models(self):
        """加载预训练模型列表"""
        models = self.trainer.get_pretrained_models()
        self.model_combo.clear()
        self.model_combo.addItems(list(models.keys()))

    def download_model(self):
        """下载预训练模型"""
        model_name = self.model_combo.currentText()
        if not model_name:
            QMessageBox.warning(self, "警告", "请选择要下载的模型")
            return

        # 创建models目录
        models_dir = "models"
        os.makedirs(models_dir, exist_ok=True)

        # 开始下载
        self.download_model_btn.setEnabled(False)
        self.download_thread = ModelDownloadThread(self.trainer, model_name, models_dir)
        self.download_thread.progressUpdate.connect(self.on_download_progress)
        self.download_thread.finished.connect(self.on_download_finished)
        self.download_thread.start()

    def on_download_progress(self, progress, message):
        """下载进度更新"""
        pass  # 简化版不显示进度

    def on_download_finished(self, success, message):
        """下载完成"""
        self.download_model_btn.setEnabled(True)

        if success:
            self.model_status_label.setText(f"模型已加载: {self.model_combo.currentText()}")
            self.model_status_label.setStyleSheet("color: green;")
            self.update_model_info()
            self.update_training_buttons()
        else:
            self.model_status_label.setText("模型下载失败")
            self.model_status_label.setStyleSheet("color: red;")
            QMessageBox.warning(self, "下载失败", message)

    def browse_custom_model(self):
        """浏览自定义模型文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择模型文件",
            "",
            "模型文件 (*.pt *.pth *.onnx);;所有文件 (*)"
        )

        if file_path:
            self.custom_model_path.setText(file_path)

    def load_custom_model(self):
        """加载自定义模型"""
        model_path = self.custom_model_path.text().strip()
        if not model_path:
            QMessageBox.warning(self, "警告", "请选择模型文件")
            return

        if not os.path.exists(model_path):
            QMessageBox.warning(self, "警告", "模型文件不存在")
            return

        # 加载模型
        success = self.trainer.load_custom_model(model_path)

        if success:
            self.model_status_label.setText(f"自定义模型已加载: {os.path.basename(model_path)}")
            self.model_status_label.setStyleSheet("color: green;")
            self.update_model_info()
            self.update_training_buttons()
        else:
            self.model_status_label.setText("自定义模型加载失败")
            self.model_status_label.setStyleSheet("color: red;")

    def browse_dataset(self):
        """浏览数据集目录"""
        dir_path = QFileDialog.getExistingDirectory(
            self,
            "选择数据集目录",
            "",
            QFileDialog.ShowDirsOnly
        )

        if dir_path:
            self.dataset_path.setText(dir_path)
            self.current_dataset = dir_path

    def validate_dataset(self):
        """验证数据集"""
        dataset_path = self.dataset_path.text().strip()
        if not dataset_path:
            QMessageBox.warning(self, "警告", "请选择数据集目录")
            return

        if not os.path.exists(dataset_path):
            QMessageBox.warning(self, "警告", "数据集目录不存在")
            return

        # 验证数据集结构
        validation_result = self.trainer.validate_dataset(dataset_path)

        if validation_result['valid']:
            # 格式化数据集信息显示
            info_text = "数据集验证成功！\n\n"

            # 显示基本信息
            info_text += "目录结构:\n"
            info_text += f"- 训练集: train/\n"
            info_text += f"- 验证集: val/\n"

            # 显示配置信息
            if 'data_config' in validation_result['info']:
                config = validation_result['info']['data_config']
                info_text += f"\n配置信息:\n"
                info_text += f"- 类别数量: {config.get('nc', 'Unknown')}\n"
                if 'names' in config:
                    info_text += f"- 类别名称: {', '.join(config['names'][:5])}{'...' if len(config['names']) > 5 else ''}\n"

            # 显示警告信息
            if validation_result['warnings']:
                info_text += f"\n警告:\n"
                for warning in validation_result['warnings']:
                    info_text += f"- {warning}\n"

            self.dataset_info_text.setText(info_text)
            self.create_data_config(dataset_path, validation_result)
            # 更新训练按钮状态
            self.update_training_buttons()
        else:
            # 显示错误信息
            error_text = "数据集验证失败！\n\n"
            error_text += "错误:\n"
            for error in validation_result['errors']:
                error_text += f"- {error}\n"

            if validation_result['warnings']:
                error_text += f"\n警告:\n"
                for warning in validation_result['warnings']:
                    error_text += f"- {warning}\n"

            self.dataset_info_text.setText(error_text)
            QMessageBox.warning(self, "数据集验证失败", "\n".join(validation_result['errors']))

    def create_data_config(self, dataset_path, validation_result):
        """创建数据配置文件"""
        try:
            # 检查是否已有配置文件
            data_yaml = os.path.join(dataset_path, 'data.yaml')

            if os.path.exists(data_yaml):
                self.current_data_config = data_yaml
                return

            # 尝试从现有标签文件推断类别
            class_names = self.infer_class_names(dataset_path)

            if class_names:
                # 创建配置文件
                config_content = f"""# YOLO数据集配置文件
path: {dataset_path}
train: train/images
val: val/images

nc: {len(class_names)}
names: {class_names}
"""

                config_file = os.path.join(dataset_path, 'data.yaml')
                with open(config_file, 'w', encoding='utf-8') as f:
                    f.write(config_content)

                if config_file:
                    self.current_data_config = config_file

        except Exception as e:
            QMessageBox.warning(self, "错误", f"创建配置文件失败: {str(e)}")

    def infer_class_names(self, dataset_path):
        """从标签文件推断类别名称"""
        try:
            # 查找标签文件
            train_labels = os.path.join(dataset_path, 'train', 'labels')
            if not os.path.exists(train_labels):
                return []

            # 读取所有标签文件，收集类别ID
            class_ids = set()
            for label_file in os.listdir(train_labels):
                if label_file.endswith('.txt'):
                    label_path = os.path.join(train_labels, label_file)
                    with open(label_path, 'r') as f:
                        for line in f:
                            parts = line.strip().split()
                            if parts:
                                class_ids.add(int(parts[0]))

            # 生成类别名称
            class_names = [f"class_{i}" for i in sorted(class_ids)]
            return class_names

        except Exception:
            return []

    def start_training(self):
        """开始训练"""
        if self.trainer.model is None:
            QMessageBox.warning(self, "警告", "请先加载模型")
            return

        if not self.current_data_config:
            QMessageBox.warning(self, "警告", "请先验证数据集")
            return

        # 应用训练参数
        self.apply_training_params()

        # 更新UI状态
        self.start_training_btn.setEnabled(False)
        self.stop_training_btn.setEnabled(True)
        self.validate_model_btn.setEnabled(False)

        # 启动训练线程
        self.training_thread = TrainingThread(self.trainer, self.current_data_config)
        self.training_thread.progressUpdate.connect(self.on_training_progress)
        self.training_thread.metricsUpdate.connect(self.on_training_metrics)
        self.training_thread.logUpdate.connect(self.on_training_log)
        self.training_thread.finished.connect(self.on_training_finished)

        # 启动训练进度组件
        epochs = self.epochs_spin.value()
        self.progress_widget.start_training(epochs)

        self.training_thread.start()

    def apply_training_params(self):
        """应用训练参数到trainer"""
        try:
            # 收集所有训练参数
            training_config = {
                'epochs': self.epochs_spin.value(),
                'batch': self.batch_spin.value(),
                'imgsz': self.imgsz_spin.value(),
                'device': self.device_combo.currentText(),
                'lr0': self.lr0_spin.value(),
                'lrf': self.lrf_spin.value(),
                'momentum': self.momentum_spin.value(),
                'weight_decay': self.weight_decay_spin.value(),
                'warmup_epochs': self.warmup_epochs_spin.value(),
                'workers': self.workers_spin.value(),
                'optimizer': self.optimizer_combo.currentText(),
                'amp': self.amp_check.isChecked(),
                'cache': self.cache_check.isChecked(),
                'plots': self.plots_check.isChecked(),
            }

            # 应用到trainer
            self.trainer.training_config.update(training_config)

        except Exception as e:
            QMessageBox.warning(self, "参数错误", f"应用训练参数失败: {str(e)}")

    def stop_training(self):
        """强制停止训练"""
        if self.training_thread and self.training_thread.isRunning():
            # 显示确认对话框
            reply = QMessageBox.question(
                self,
                "确认停止训练",
                "确定要强制停止训练吗？\n这将立即终止训练进程。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                try:
                    print("开始强制停止训练...")

                    # 第一步：设置停止标志
                    self.training_thread.stop_training()

                    # 第二步：强制终止线程
                    print("强制终止训练线程...")
                    self.training_thread.terminate()

                    # 第三步：等待线程结束
                    if not self.training_thread.wait(3000):  # 等待3秒
                        print("线程未能正常结束，强制杀死...")
                        # 如果线程仍未结束，直接清理

                    print("训练已强制停止")

                except Exception as e:
                    print(f"强制停止训练时出错: {e}")

                # 清理训练线程引用
                self.training_thread = None

                # 更新按钮状态
                self.update_training_buttons()

                QMessageBox.information(self, "训练已停止", "训练已被强制停止")

    def on_training_progress(self, progress, message):
        """训练进度更新"""
        # 更新进度组件的轮次进度
        if hasattr(self, 'progress_widget'):
            # 从消息中提取当前轮次信息
            try:
                if "训练轮次" in message:
                    parts = message.split()
                    for part in parts:
                        if "/" in part:
                            current, total = part.split("/")
                            current_epoch = int(current)
                            self.progress_widget.update_epoch_progress(current_epoch, progress)
                            break
            except:
                pass

    def on_training_metrics(self, metrics):
        """训练指标更新"""
        if hasattr(self, 'progress_widget'):
            self.progress_widget.update_training_metrics(metrics)

    def on_training_log(self, message):
        """训练日志更新"""
        # 可以在这里添加日志显示逻辑
        print(f"训练日志: {message}")

    def on_training_finished(self, success, message):
        """训练完成"""
        # 清理训练线程引用
        self.training_thread = None

        # 完成训练进度组件
        if hasattr(self, 'progress_widget'):
            self.progress_widget.finish_training(success)

        # 更新按钮状态
        self.update_training_buttons()

        if success:
            QMessageBox.information(self, "训练完成", "模型训练成功完成！")
        else:
            QMessageBox.warning(self, "训练失败", f"训练失败: {message}")

    def validate_model(self):
        """验证模型"""
        if self.trainer.model is None:
            QMessageBox.warning(self, "警告", "请先加载模型")
            return

        results = self.trainer.validate_model(self.current_data_config)

        if results:
            QMessageBox.information(self, "验证完成", "模型验证完成，请查看终端输出")
        else:
            QMessageBox.warning(self, "验证失败", "模型验证失败")

    def update_model_info(self):
        """更新模型信息显示"""
        info = self.trainer.get_model_info()

        if info and 'error' not in info:
            info_text = f"模型路径: {info.get('model_path', 'Unknown')}\n"
            info_text += f"模型类型: {info.get('model_type', 'PyTorch')}\n"
            info_text += f"任务类型: {info.get('task', 'Unknown')}\n"
            info_text += f"设备: {info.get('device', 'Unknown')}\n"

            if 'num_classes' in info:
                info_text += f"类别数量: {info.get('num_classes', 'Unknown')}\n"

            if 'classes' in info and info['classes']:
                class_names = list(info['classes'].values())[:5]  # 显示前5个类别
                info_text += f"类别示例: {', '.join(class_names)}{'...' if len(info['classes']) > 5 else ''}"

            self.model_info_text.setText(info_text)
        else:
            error_msg = info.get('error', '无法获取模型信息') if info else '无法获取模型信息'
            self.model_info_text.setText(f"错误: {error_msg}")

    def update_training_buttons(self):
        """更新训练按钮状态"""
        model_loaded = self.trainer.model is not None
        dataset_ready = self.current_data_config is not None
        is_training = self.training_thread is not None and self.training_thread.isRunning()

        # 开始训练按钮：需要模型和数据集都准备好，且当前没有在训练
        self.start_training_btn.setEnabled(model_loaded and dataset_ready and not is_training)

        # 停止训练按钮：只有在训练时才启用
        self.stop_training_btn.setEnabled(is_training)

        # 验证模型按钮：需要模型加载，且当前没有在训练
        self.validate_model_btn.setEnabled(model_loaded and not is_training)
