#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试验证回调是否被正确调用
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

from PySide6.QtWidgets import QApplication
from ui.widgets.training_progress_widget import MetricsDisplayWidget

def test_validation_callback():
    """测试验证回调功能"""
    print("=" * 60)
    print("🔍 验证回调测试")
    print("=" * 60)
    
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 创建指标显示组件
    metrics_widget = MetricsDisplayWidget()
    print("✅ 指标显示组件创建成功")
    
    # 模拟真实的训练过程
    print("\n📊 模拟真实训练过程:")
    
    # 第1轮次 - 只有训练数据
    print("\n--- 第1轮次 - 训练阶段 ---")
    train_metrics_1 = {
        'epoch': 1,
        'train/box_loss': 1.5
    }
    print(f"发送训练指标: {train_metrics_1}")
    metrics_widget.update_metrics(train_metrics_1)
    
    # 第1轮次 - 验证数据
    print("\n--- 第1轮次 - 验证阶段 ---")
    val_metrics_1 = {
        'epoch': 1,
        'val/box_loss': 1.6,
        'metrics/precision(B)': 0.75,
        'metrics/recall(B)': 0.68,
        'metrics/mAP50(B)': 0.72,
        'metrics/mAP50-95(B)': 0.55
    }
    print(f"发送验证指标: {val_metrics_1}")
    metrics_widget.update_metrics(val_metrics_1)
    
    # 第2轮次 - 训练数据
    print("\n--- 第2轮次 - 训练阶段 ---")
    train_metrics_2 = {
        'epoch': 2,
        'train/box_loss': 1.2
    }
    print(f"发送训练指标: {train_metrics_2}")
    metrics_widget.update_metrics(train_metrics_2)
    
    # 第2轮次 - 验证数据
    print("\n--- 第2轮次 - 验证阶段 ---")
    val_metrics_2 = {
        'epoch': 2,
        'val/box_loss': 1.3,
        'metrics/precision(B)': 0.82,
        'metrics/recall(B)': 0.76,
        'metrics/mAP50(B)': 0.79,
        'metrics/mAP50-95(B)': 0.63
    }
    print(f"发送验证指标: {val_metrics_2}")
    metrics_widget.update_metrics(val_metrics_2)
    
    # 第3轮次 - 训练数据
    print("\n--- 第3轮次 - 训练阶段 ---")
    train_metrics_3 = {
        'epoch': 3,
        'train/box_loss': 0.9
    }
    print(f"发送训练指标: {train_metrics_3}")
    metrics_widget.update_metrics(train_metrics_3)
    
    # 第3轮次 - 验证数据
    print("\n--- 第3轮次 - 验证阶段 ---")
    val_metrics_3 = {
        'epoch': 3,
        'val/box_loss': 1.1,
        'metrics/precision(B)': 0.87,
        'metrics/recall(B)': 0.83,
        'metrics/mAP50(B)': 0.85,
        'metrics/mAP50-95(B)': 0.71
    }
    print(f"发送验证指标: {val_metrics_3}")
    metrics_widget.update_metrics(val_metrics_3)
    
    print("\n" + "=" * 60)
    print("🎯 验证回调测试完成")
    print("=" * 60)
    print("检查结果:")
    print("1. 是否所有指标都被正确处理?")
    print("2. 历史记录是否正确建立?")
    print("3. 绘图函数是否被正确调用?")
    
    # 检查历史记录
    print("\n📊 最终历史记录状态:")
    history = metrics_widget.metrics_history
    for key, values in history.items():
        print(f"  {key}: {len(values)} 个数据点 -> {values}")
    
    return True

if __name__ == "__main__":
    test_validation_callback()
