#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试：四条曲线图修复验证
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
from PySide6.QtCore import QTimer
from ui.widgets.training_progress_widget import TrainingProgressWidget

class FinalTestWindow(QMainWindow):
    """最终测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("四条曲线图修复验证 - 最终测试")
        self.setGeometry(100, 100, 1400, 1000)
        
        # 创建中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("🎯 四条曲线图修复验证 - 最终测试")
        title_label.setStyleSheet("font-size: 20px; font-weight: bold; color: #2E86AB; padding: 15px;")
        layout.addWidget(title_label)
        
        # 说明
        desc_label = QLabel("✅ 修复完成！现在应该能看到完整的四条曲线图实时绘制")
        desc_label.setStyleSheet("font-size: 14px; color: #27AE60; padding: 10px; background-color: #E8F8F5; border-radius: 5px;")
        layout.addWidget(desc_label)
        
        # 预期效果说明
        expected_label = QLabel("""
📊 预期效果：
• 左上角：训练损失曲线 (红色) + 验证损失曲线 (蓝色)
• 右上角：精确率曲线 (绿色)，从低到高
• 左下角：召回率曲线 (橙色)，从低到高  
• 右下角：mAP@0.5曲线 (紫色) + mAP@0.5:0.95曲线 (棕色)
        """)
        expected_label.setStyleSheet("font-size: 12px; color: #34495E; padding: 10px; background-color: #F8F9FA; border-radius: 5px;")
        layout.addWidget(expected_label)
        
        # 创建训练进度组件
        self.progress_widget = TrainingProgressWidget()
        layout.addWidget(self.progress_widget)
        
        # 控制按钮
        self.start_btn = QPushButton("🚀 开始最终验证测试")
        self.start_btn.setStyleSheet("""
            QPushButton { 
                background-color: #27AE60; 
                color: white; 
                font-size: 16px; 
                font-weight: bold;
                padding: 15px; 
                border-radius: 8px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        layout.addWidget(self.start_btn)
        
        # 连接信号
        self.start_btn.clicked.connect(self.start_test)
        
        # 测试定时器
        self.timer = QTimer()
        self.timer.timeout.connect(self.add_realistic_data)
        self.epoch_counter = 0
        
    def start_test(self):
        """开始最终测试"""
        self.epoch_counter = 0
        self.progress_widget.start_training(8)
        
        # 启动定时器，每2秒一个epoch
        self.timer.start(2000)
        self.start_btn.setEnabled(False)
        self.start_btn.setText("🔄 测试进行中...")
        
    def add_realistic_data(self):
        """添加真实的训练数据模拟"""
        self.epoch_counter += 1
        
        import random
        import math
        
        # 模拟真实的训练过程
        base_train_loss = 2.0 * math.exp(-self.epoch_counter * 0.3) + 0.1
        base_val_loss = base_train_loss * 1.1 + random.uniform(-0.05, 0.05)
        
        # 训练阶段数据
        train_metrics = {
            'epoch': self.epoch_counter,
            'train/box_loss': base_train_loss + random.uniform(-0.05, 0.05)
        }
        
        print(f"\n=== Epoch {self.epoch_counter} ===")
        print(f"训练阶段: {train_metrics}")
        
        # 更新训练指标
        self.progress_widget.update_training_metrics(train_metrics)
        
        # 验证阶段数据 - 模拟真实的验证指标
        precision = min(0.95, 0.3 + (self.epoch_counter * 0.08) + random.uniform(-0.02, 0.02))
        recall = min(0.95, 0.25 + (self.epoch_counter * 0.09) + random.uniform(-0.02, 0.02))
        map50 = min(0.9, 0.2 + (self.epoch_counter * 0.1) + random.uniform(-0.02, 0.02))
        map50_95 = min(0.8, 0.15 + (self.epoch_counter * 0.08) + random.uniform(-0.02, 0.02))
        
        val_metrics = {
            'epoch': self.epoch_counter,
            'val/box_loss': base_val_loss,
            'metrics/precision(B)': precision,
            'metrics/recall(B)': recall,
            'metrics/mAP50(B)': map50,
            'metrics/mAP50-95(B)': map50_95
        }
        
        print(f"验证阶段: {val_metrics}")
        
        # 更新验证指标
        self.progress_widget.update_training_metrics(val_metrics)
        
        # 更新轮次进度
        self.progress_widget.update_epoch_progress(self.epoch_counter, 100)
        
        # 检查是否完成
        if self.epoch_counter >= 8:
            self.timer.stop()
            self.progress_widget.finish_training(True)
            self.start_btn.setEnabled(True)
            self.start_btn.setText("✅ 测试完成 - 重新开始")
            
            print("\n" + "="*60)
            print("🎉 最终验证测试完成！")
            print("="*60)
            print("检查结果：")
            print("✅ 左上角应该显示红色训练损失曲线和蓝色验证损失曲线")
            print("✅ 右上角应该显示绿色精确率曲线，呈上升趋势")
            print("✅ 左下角应该显示橙色召回率曲线，呈上升趋势")
            print("✅ 右下角应该显示紫色mAP50和棕色mAP50-95曲线，呈上升趋势")
            print("="*60)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = FinalTestWindow()
    window.show()
    
    print("🎯 四条曲线图修复验证 - 最终测试")
    print("="*60)
    print("修复内容：")
    print("1. ✅ 修复了验证指标无法添加到历史记录的问题")
    print("2. ✅ 修复了数据长度不匹配导致曲线无法绘制的问题")
    print("3. ✅ 优化了图表布局和视觉效果")
    print("4. ✅ 完善了指标更新逻辑")
    print("="*60)
    print("点击'开始最终验证测试'按钮查看修复效果")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
