#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的四条曲线图功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
from PySide6.QtCore import QTimer
from ui.widgets.training_progress_widget import TrainingProgressWidget

class FixedPlotsTestWindow(QMainWindow):
    """修复后的曲线图测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("修复后的四条曲线图测试")
        self.setGeometry(100, 100, 1400, 1000)
        
        # 创建中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("修复后的四条曲线图测试")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #2E86AB; padding: 10px;")
        layout.addWidget(title_label)
        
        # 说明
        desc_label = QLabel("测试验证指标的正确提取和四条曲线的实时绘制")
        desc_label.setStyleSheet("font-size: 12px; color: #666; padding: 5px;")
        layout.addWidget(desc_label)
        
        # 创建训练进度组件
        self.progress_widget = TrainingProgressWidget()
        layout.addWidget(self.progress_widget)
        
        # 控制按钮
        self.start_btn = QPushButton("开始测试 - 模拟完整训练数据")
        self.start_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-size: 14px; padding: 10px; }")
        layout.addWidget(self.start_btn)
        
        # 连接信号
        self.start_btn.clicked.connect(self.start_test)
        
        # 测试定时器
        self.timer = QTimer()
        self.timer.timeout.connect(self.add_complete_data)
        self.epoch_counter = 0
        
    def start_test(self):
        """开始测试"""
        self.epoch_counter = 0
        self.progress_widget.start_training(10)
        
        # 启动定时器，每1.5秒一个epoch
        self.timer.start(1500)
        self.start_btn.setEnabled(False)
        
    def add_complete_data(self):
        """添加完整的训练数据（包括验证指标）"""
        self.epoch_counter += 1
        
        import random
        
        # 模拟训练阶段数据
        train_loss = max(0.1, 2.0 - (self.epoch_counter * 0.15) + random.uniform(-0.1, 0.1))
        
        train_metrics = {
            'epoch': self.epoch_counter,
            'train/box_loss': train_loss,
            'train/cls_loss': max(0.1, 1.5 - (self.epoch_counter * 0.1) + random.uniform(-0.05, 0.05)),
            'train/dfl_loss': max(0.1, 1.2 - (self.epoch_counter * 0.08) + random.uniform(-0.05, 0.05))
        }
        
        print(f"\n=== Epoch {self.epoch_counter} ===")
        print(f"训练阶段数据: {train_metrics}")
        
        # 更新训练指标
        self.progress_widget.update_training_metrics(train_metrics)
        
        # 模拟验证阶段数据
        val_loss = max(0.1, 1.8 - (self.epoch_counter * 0.12) + random.uniform(-0.1, 0.1))
        precision = min(0.95, 0.3 + (self.epoch_counter * 0.06) + random.uniform(-0.02, 0.02))
        recall = min(0.95, 0.25 + (self.epoch_counter * 0.07) + random.uniform(-0.02, 0.02))
        map50 = min(0.9, 0.2 + (self.epoch_counter * 0.08) + random.uniform(-0.02, 0.02))
        map50_95 = min(0.8, 0.15 + (self.epoch_counter * 0.06) + random.uniform(-0.02, 0.02))
        
        val_metrics = {
            'epoch': self.epoch_counter,
            'val/box_loss': val_loss,
            'val/cls_loss': max(0.1, 1.3 - (self.epoch_counter * 0.08) + random.uniform(-0.05, 0.05)),
            'val/dfl_loss': max(0.1, 1.1 - (self.epoch_counter * 0.07) + random.uniform(-0.05, 0.05)),
            'metrics/precision(B)': precision,
            'metrics/recall(B)': recall,
            'metrics/mAP50(B)': map50,
            'metrics/mAP50-95(B)': map50_95
        }
        
        print(f"验证阶段数据: {val_metrics}")
        
        # 更新验证指标
        self.progress_widget.update_training_metrics(val_metrics)
        
        # 更新轮次进度
        self.progress_widget.update_epoch_progress(self.epoch_counter, 100)
        
        # 检查是否完成
        if self.epoch_counter >= 10:
            self.timer.stop()
            self.progress_widget.finish_training(True)
            self.start_btn.setEnabled(True)
            print("\n✅ 测试完成！")
            print("应该可以看到四条曲线图都有数据：")
            print("1. 训练损失曲线 (左上) - 红色训练损失 + 蓝色验证损失")
            print("2. 精确率曲线 (右上) - 绿色精确率")
            print("3. 召回率曲线 (左下) - 橙色召回率")
            print("4. mAP指标曲线 (右下) - 紫色mAP50 + 棕色mAP50-95")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = FixedPlotsTestWindow()
    window.show()
    
    print("修复后的四条曲线图测试程序已启动")
    print("点击'开始测试'按钮验证修复效果")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
