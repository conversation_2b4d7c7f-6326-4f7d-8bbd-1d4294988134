# 训练进度可视化组件
from typing import Dict
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGroupBox,
                               QLabel, QProgressBar, QGridLayout)
from PySide6.QtCore import QTimer
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure

# 设置matplotlib中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class MetricsDisplayWidget(QWidget):
    """训练指标显示组件"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.metrics_history = {
            'epoch': [],
            'train_loss': [],
            'val_loss': [],
            'precision': [],
            'recall': [],
            'mAP50': [],
            'mAP50_95': []
        }
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 当前指标显示
        current_metrics_group = QGroupBox("当前训练指标")
        current_layout = QGridLayout(current_metrics_group)
        
        # 创建指标标签
        self.metric_labels = {}
        metrics = [
            ('epoch', '轮次'),
            ('train_loss', '训练损失'),
            ('val_loss', '验证损失'),
            ('precision', '精确率'),
            ('recall', '召回率'),
            ('mAP50', 'mAP@0.5'),
            ('mAP50_95', 'mAP@0.5:0.95')
        ]
        
        for i, (key, name) in enumerate(metrics):
            row = i // 2
            col = (i % 2) * 2
            
            name_label = QLabel(f"{name}:")
            name_label.setStyleSheet("font-weight: bold;")
            value_label = QLabel("--")
            value_label.setStyleSheet("color: blue; font-family: monospace;")
            
            current_layout.addWidget(name_label, row, col)
            current_layout.addWidget(value_label, row, col + 1)
            
            self.metric_labels[key] = value_label
        
        layout.addWidget(current_metrics_group)
        
        # 训练曲线图
        self.setup_plots()
        layout.addWidget(self.plot_widget)
    
    def setup_plots(self):
        """设置训练曲线图"""
        self.plot_widget = QWidget()
        plot_layout = QVBoxLayout(self.plot_widget)

        # 创建matplotlib图形 - 调整大小和DPI
        self.figure = Figure(figsize=(14, 10), dpi=80)
        self.canvas = FigureCanvas(self.figure)
        plot_layout.addWidget(self.canvas)

        # 创建子图 - 调整间距
        self.loss_ax = self.figure.add_subplot(2, 2, 1)
        self.precision_ax = self.figure.add_subplot(2, 2, 2)
        self.recall_ax = self.figure.add_subplot(2, 2, 3)
        self.map_ax = self.figure.add_subplot(2, 2, 4)

        # 设置图表标题和样式
        self.loss_ax.set_title('训练损失', fontsize=14, fontweight='bold', pad=15)
        self.precision_ax.set_title('精确率', fontsize=14, fontweight='bold', pad=15)
        self.recall_ax.set_title('召回率', fontsize=14, fontweight='bold', pad=15)
        self.map_ax.set_title('mAP指标', fontsize=14, fontweight='bold', pad=15)

        # 设置Y轴范围
        self.precision_ax.set_ylim(0, 1)
        self.recall_ax.set_ylim(0, 1)
        self.map_ax.set_ylim(0, 1)

        # 设置标签和网格
        for ax in [self.loss_ax, self.precision_ax, self.recall_ax, self.map_ax]:
            ax.set_xlabel('Epoch', fontsize=12)
            ax.grid(True, alpha=0.3, linestyle='--')
            ax.tick_params(axis='both', which='major', labelsize=10)

        # 设置Y轴标签
        self.loss_ax.set_ylabel('Loss', fontsize=12)
        self.precision_ax.set_ylabel('Precision', fontsize=12)
        self.recall_ax.set_ylabel('Recall', fontsize=12)
        self.map_ax.set_ylabel('mAP', fontsize=12)

        # 调整子图间距
        self.figure.subplots_adjust(left=0.08, bottom=0.08, right=0.95, top=0.92,
                                   wspace=0.25, hspace=0.35)
    
    def update_metrics(self, metrics: Dict[str, float]):
        """更新训练指标"""
        # 映射CSV列名到显示标签
        csv_to_label_mapping = {
            'epoch': 'epoch',
            'train/box_loss': 'train_loss',
            'val/box_loss': 'val_loss',
            'metrics/precision(B)': 'precision',
            'metrics/recall(B)': 'recall',
            'metrics/mAP50(B)': 'mAP50',
            'metrics/mAP50-95(B)': 'mAP50_95'
        }

        # 更新当前指标显示
        for csv_key, value in metrics.items():
            label_key = csv_to_label_mapping.get(csv_key, csv_key)
            if label_key in self.metric_labels:
                if isinstance(value, float):
                    self.metric_labels[label_key].setText(f"{value:.4f}")
                else:
                    self.metric_labels[label_key].setText(str(value))

        # 检查是否是新的轮次数据
        current_epoch = metrics.get('epoch', 0)
        is_new_epoch = not self.metrics_history['epoch'] or current_epoch > self.metrics_history['epoch'][-1]

        if is_new_epoch:
            # 添加到历史记录（使用映射后的键名）
            for csv_key, value in metrics.items():
                label_key = csv_to_label_mapping.get(csv_key, csv_key)
                if label_key in self.metrics_history:
                    self.metrics_history[label_key].append(value)
        else:
            # 更新当前轮次的数据 - 修复逻辑
            for csv_key, value in metrics.items():
                label_key = csv_to_label_mapping.get(csv_key, csv_key)
                if label_key in self.metrics_history:
                    if self.metrics_history[label_key]:
                        # 如果有历史数据，更新最后一个
                        self.metrics_history[label_key][-1] = value
                    else:
                        # 如果没有历史数据，添加新数据
                        self.metrics_history[label_key].append(value)

        # 更新图表
        self.update_plots()
    
    def update_plots(self):
        """更新训练曲线图"""
        try:
            if not self.metrics_history['epoch']:
                return

            epochs = self.metrics_history['epoch']

            # 清除之前的图表
            self.loss_ax.clear()
            self.precision_ax.clear()
            self.recall_ax.clear()
            self.map_ax.clear()

            # 绘制损失曲线
            train_loss_data = self.metrics_history['train_loss']
            val_loss_data = self.metrics_history['val_loss']

            if train_loss_data and len(train_loss_data) == len(epochs):
                self.loss_ax.plot(epochs, train_loss_data, label='训练损失', color='red', linewidth=2, marker='o')

            if val_loss_data and len(val_loss_data) == len(epochs):
                self.loss_ax.plot(epochs, val_loss_data, label='验证损失', color='blue', linewidth=2, marker='s')

            self.loss_ax.set_title('训练损失', fontsize=12, fontweight='bold')
            self.loss_ax.set_xlabel('Epoch')
            self.loss_ax.set_ylabel('Loss')
            if self.loss_ax.get_lines():
                self.loss_ax.legend()
            self.loss_ax.grid(True, alpha=0.3)

            # 绘制精确率曲线
            precision_data = self.metrics_history['precision']
            if precision_data and len(precision_data) == len(epochs):
                self.precision_ax.plot(epochs, precision_data, label='精确率', color='green', linewidth=2, marker='o')

            self.precision_ax.set_title('精确率', fontsize=12, fontweight='bold')
            self.precision_ax.set_xlabel('Epoch')
            self.precision_ax.set_ylabel('Precision')
            self.precision_ax.set_ylim(0, 1)
            if self.precision_ax.get_lines():
                self.precision_ax.legend()
            self.precision_ax.grid(True, alpha=0.3)

            # 绘制召回率曲线
            recall_data = self.metrics_history['recall']
            if recall_data and len(recall_data) == len(epochs):
                self.recall_ax.plot(epochs, recall_data, label='召回率', color='orange', linewidth=2, marker='o')

            self.recall_ax.set_title('召回率', fontsize=12, fontweight='bold')
            self.recall_ax.set_xlabel('Epoch')
            self.recall_ax.set_ylabel('Recall')
            self.recall_ax.set_ylim(0, 1)
            if self.recall_ax.get_lines():
                self.recall_ax.legend()
            self.recall_ax.grid(True, alpha=0.3)

            # 绘制mAP曲线
            map50_data = self.metrics_history['mAP50']
            map50_95_data = self.metrics_history['mAP50_95']

            if map50_data and len(map50_data) == len(epochs):
                self.map_ax.plot(epochs, map50_data, label='mAP@0.5', color='purple', linewidth=2, marker='o')

            if map50_95_data and len(map50_95_data) == len(epochs):
                self.map_ax.plot(epochs, map50_95_data, label='mAP@0.5:0.95', color='brown', linewidth=2, marker='s')

            self.map_ax.set_title('mAP指标', fontsize=12, fontweight='bold')
            self.map_ax.set_xlabel('Epoch')
            self.map_ax.set_ylabel('mAP')
            self.map_ax.set_ylim(0, 1)
            if self.map_ax.get_lines():
                self.map_ax.legend()
            self.map_ax.grid(True, alpha=0.3)

            # 重新设置Y轴范围和标签
            self.precision_ax.set_ylim(0, 1)
            self.recall_ax.set_ylim(0, 1)
            self.map_ax.set_ylim(0, 1)

            # 重新设置标签
            self.loss_ax.set_ylabel('Loss', fontsize=12)
            self.precision_ax.set_ylabel('Precision', fontsize=12)
            self.recall_ax.set_ylabel('Recall', fontsize=12)
            self.map_ax.set_ylabel('mAP', fontsize=12)

            # 调整布局并刷新画布
            self.figure.subplots_adjust(left=0.08, bottom=0.08, right=0.95, top=0.92,
                                       wspace=0.25, hspace=0.35)
            self.canvas.draw()

        except Exception as e:
            print(f"绘图错误: {e}")
            import traceback
            traceback.print_exc()
    
    def clear_metrics(self):
        """清除所有指标"""
        for key in self.metrics_history:
            self.metrics_history[key].clear()
        
        for label in self.metric_labels.values():
            label.setText("--")
        
        # 清除图表
        self.loss_ax.clear()
        self.precision_ax.clear()
        self.recall_ax.clear()
        self.map_ax.clear()
        self.canvas.draw()


class TrainingProgressWidget(QWidget):
    """训练进度主组件"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        
        # 定时器用于模拟训练进度更新
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.simulate_training_update)
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 总体进度
        progress_group = QGroupBox("训练进度")
        progress_layout = QVBoxLayout(progress_group)
        
        # 总进度条
        self.overall_progress = QProgressBar()
        self.overall_progress.setTextVisible(True)
        self.overall_progress.setFormat("总进度: %p% (%v/%m)")
        progress_layout.addWidget(QLabel("总体进度:"))
        progress_layout.addWidget(self.overall_progress)
        
        # 当前轮次进度条
        self.epoch_progress = QProgressBar()
        self.epoch_progress.setTextVisible(True)
        self.epoch_progress.setFormat("当前轮次: %p% (%v/%m)")
        progress_layout.addWidget(QLabel("当前轮次:"))
        progress_layout.addWidget(self.epoch_progress)
        
        # 状态信息
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("font-weight: bold; color: blue;")
        progress_layout.addWidget(self.status_label)
        
        # 时间信息
        time_layout = QHBoxLayout()
        self.elapsed_label = QLabel("已用时间: 00:00:00")
        self.eta_label = QLabel("预计剩余: --:--:--")
        time_layout.addWidget(self.elapsed_label)
        time_layout.addWidget(self.eta_label)
        progress_layout.addLayout(time_layout)
        
        layout.addWidget(progress_group)
        
        # 训练指标显示
        self.metrics_widget = MetricsDisplayWidget()
        layout.addWidget(self.metrics_widget)
    
    def start_training(self, total_epochs: int):
        """开始训练"""
        self.total_epochs = total_epochs
        self.overall_progress.setMaximum(total_epochs)
        self.overall_progress.setValue(0)
        self.epoch_progress.setMaximum(100)
        self.epoch_progress.setValue(0)
        self.status_label.setText("训练中...")
        self.status_label.setStyleSheet("font-weight: bold; color: green;")

        # 清除之前的指标
        self.metrics_widget.clear_metrics()

        # 初始化时间跟踪
        import time
        self.start_time = time.time()
    
    def update_epoch_progress(self, current_epoch: int, epoch_progress: int):
        """更新轮次进度"""
        self.overall_progress.setValue(current_epoch)
        self.epoch_progress.setValue(epoch_progress)

        # 更新状态显示
        self.status_label.setText(f"训练中 - 轮次 {current_epoch}/{self.overall_progress.maximum()}")
        self.status_label.setStyleSheet("font-weight: bold; color: green;")

        # 更新时间信息
        if hasattr(self, 'start_time'):
            import time
            elapsed = int(time.time() - self.start_time)
            self.update_time_info(elapsed)

            # 估算剩余时间
            if current_epoch > 0:
                avg_time_per_epoch = elapsed / current_epoch
                remaining_epochs = self.total_epochs - current_epoch
                eta = int(remaining_epochs * avg_time_per_epoch)
                self.update_time_info(elapsed, eta)
    
    def update_training_metrics(self, metrics: Dict[str, float]):
        """更新训练指标"""
        self.metrics_widget.update_metrics(metrics)
    
    def finish_training(self, success: bool = True):
        """完成训练"""
        if success:
            self.status_label.setText("训练完成")
            self.status_label.setStyleSheet("font-weight: bold; color: green;")
            self.overall_progress.setValue(self.overall_progress.maximum())
            self.epoch_progress.setValue(100)
        else:
            self.status_label.setText("训练失败")
            self.status_label.setStyleSheet("font-weight: bold; color: red;")
    
    def update_time_info(self, elapsed_seconds: int, eta_seconds: int = None):
        """更新时间信息"""
        elapsed_time = self.format_time(elapsed_seconds)
        self.elapsed_label.setText(f"已用时间: {elapsed_time}")
        
        if eta_seconds is not None:
            eta_time = self.format_time(eta_seconds)
            self.eta_label.setText(f"预计剩余: {eta_time}")
    
    def format_time(self, seconds: int) -> str:
        """格式化时间显示"""
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        seconds = seconds % 60
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
    
    def simulate_training_update(self):
        """模拟训练更新（用于测试）"""
        import random
        
        # 模拟指标更新
        metrics = {
            'epoch': random.randint(1, 100),
            'train_loss': random.uniform(0.1, 2.0),
            'val_loss': random.uniform(0.1, 2.0),
            'precision': random.uniform(0.5, 0.95),
            'recall': random.uniform(0.5, 0.95),
            'mAP50': random.uniform(0.3, 0.9),
            'mAP50_95': random.uniform(0.2, 0.8)
        }
        
        self.update_training_metrics(metrics)
        self.update_epoch_progress(metrics['epoch'], random.randint(0, 100))
        self.update_time_info(random.randint(0, 3600), random.randint(0, 1800))
    
    def start_demo(self):
        """开始演示模式"""
        self.start_training(100)
        self.update_timer.start(1000)  # 每秒更新一次
    
    def stop_demo(self):
        """停止演示模式"""
        self.update_timer.stop()
        self.finish_training()
